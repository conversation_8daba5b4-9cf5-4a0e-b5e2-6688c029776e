{"pages": ["pages/test/test", "pages/index/index", "pages/chat/chat", "pages/chatBot/chatBot", "pages/foodBuy/foodBuy"], "usingComponents": {"agent-ui": "/components/agent-ui/index", "progress-ring": "/components/resume/progress-ring/index", "resume-card": "/components/resume/resume-card/index", "skill-tag": "/components/resume/skill-tag/index", "template-selector": "/components/resume/template-selector/index", "custom-map": "/components/toolCard/map/index", "custom-weather": "/components/toolCard/weather/index", "custom-food-list": "/components/toolCard/food-list/index", "custom-business-list": "/components/toolCard/business-list/index", "ui-button": "/components/ui/button/index"}, "window": {"navigationBarBackgroundColor": "#0ea5e9", "navigationBarTextStyle": "white", "navigationBarTitleText": "简历故事", "backgroundColor": "#f8fafc", "backgroundTextStyle": "light", "enablePullDownRefresh": true}, "sitemapLocation": "sitemap.json", "style": "v2", "lazyCodeLoading": "requiredComponents", "networkTimeout": {"request": 300000, "connectSocket": 60000, "uploadFile": 60000, "downloadFile": 60000}}