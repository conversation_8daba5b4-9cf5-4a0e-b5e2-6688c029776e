/* components/ui/button/index.wxss */

/**
 * 统一按钮组件样式
 * 基于设计系统的按钮规范
 */

.ui-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-radius: var(--radius-base);
  font-weight: var(--font-weight-medium);
  transition: all var(--duration-fast) var(--ease-out);
  cursor: pointer;
  border: none;
  outline: none;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

/* 按钮变体 */
.ui-button.primary {
  background-color: var(--color-accent);
  color: var(--color-accent-foreground);
}

.ui-button.primary:not(.disabled):hover {
  background-color: var(--color-foreground-muted);
}

.ui-button.secondary {
  background-color: var(--color-background-muted);
  color: var(--color-foreground);
  border: 1px solid var(--color-border);
}

.ui-button.secondary:not(.disabled):hover {
  background-color: var(--color-background-subtle);
  border-color: var(--color-border);
}

.ui-button.ghost {
  background-color: transparent;
  color: var(--color-foreground-muted);
}

.ui-button.ghost:not(.disabled):hover {
  background-color: var(--color-background-subtle);
  color: var(--color-foreground);
}

.ui-button.destructive {
  background-color: var(--color-destructive);
  color: var(--color-accent-foreground);
}

.ui-button.destructive:not(.disabled):hover {
  opacity: 0.9;
}

/* 按钮尺寸 */
.ui-button.sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  min-height: 64rpx;
}

.ui-button.default {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-sm);
  min-height: 88rpx;
}

.ui-button.lg {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-base);
  min-height: 96rpx;
}

/* 按钮状态 */
.ui-button.disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.ui-button-hover {
  transform: translateY(-1rpx);
}

/* 加载状态 */
.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: var(--spacing-2);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.button-text {
  line-height: 1;
}
