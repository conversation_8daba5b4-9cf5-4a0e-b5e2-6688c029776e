/**
 * 简历故事 - 设计系统
 * 基于乔布斯和乔纳森的设计理念
 * 以信息为核心，辅以讲故事的氛围
 */

/* ========================================
   设计令牌 (Design Tokens)
   ======================================== */

:root {
  /* 颜色系统 - 基于语义化的单色调色板 */
  --color-background: #ffffff;
  --color-background-subtle: #fafafa;
  --color-background-muted: #f5f5f5;

  --color-foreground: #0a0a0a;
  --color-foreground-muted: #737373;
  --color-foreground-subtle: #a3a3a3;

  --color-border: #e5e5e5;
  --color-border-subtle: #f0f0f0;

  --color-accent: #0a0a0a;
  --color-accent-foreground: #ffffff;

  /* 专注色彩 - 仅在必要时使用 */
  --color-primary: #2563eb;
  --color-primary-foreground: #ffffff;

  --color-success: #16a34a;
  --color-warning: #d97706;
  --color-destructive: #dc2626;

  /* 字体系统 - 清晰的层级 */
  --font-size-xs: 24rpx;
  --font-size-sm: 28rpx;
  --font-size-base: 32rpx;
  --font-size-lg: 36rpx;
  --font-size-xl: 40rpx;
  --font-size-2xl: 48rpx;
  --font-size-3xl: 60rpx;
  --font-size-4xl: 72rpx;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* 间距系统 - 基于 8px 网格 */
  --spacing-1: 8rpx;
  --spacing-2: 16rpx;
  --spacing-3: 24rpx;
  --spacing-4: 32rpx;
  --spacing-5: 40rpx;
  --spacing-6: 48rpx;
  --spacing-8: 64rpx;
  --spacing-10: 80rpx;
  --spacing-12: 96rpx;
  --spacing-16: 128rpx;
  --spacing-20: 160rpx;
  --spacing-24: 192rpx;

  /* 圆角系统 */
  --radius-sm: 6rpx;
  --radius-base: 12rpx;
  --radius-lg: 16rpx;
  --radius-xl: 24rpx;

  /* 阴影系统 - 极简且有意义 */
  --shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  --shadow-base: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);

  /* 动画系统 */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;

  --ease-out: cubic-bezier(0.16, 1, 0.3, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

/* ========================================
   基础重置
   ======================================== */

page {
  background-color: var(--color-background);
  color: var(--color-foreground);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 移除所有默认样式 */
view, text, button, input, textarea, scroll-view {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* ========================================
   基础组件
   ======================================== */

/* 按钮系统 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--duration-fast) var(--ease-out);
  cursor: pointer;
  border: none;
  outline: none;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background-color: var(--color-accent);
  color: var(--color-accent-foreground);
  padding: var(--spacing-3) var(--spacing-6);
  min-height: 88rpx;
}

.btn-primary:hover {
  background-color: var(--color-foreground-muted);
}

.btn-secondary {
  background-color: var(--color-background-muted);
  color: var(--color-foreground);
  padding: var(--spacing-3) var(--spacing-6);
  min-height: 88rpx;
  border: 1px solid var(--color-border);
}

.btn-ghost {
  background-color: transparent;
  color: var(--color-foreground-muted);
  padding: var(--spacing-2) var(--spacing-4);
}

.btn-ghost:hover {
  background-color: var(--color-background-subtle);
  color: var(--color-foreground);
}

/* 卡片系统 */
.card {
  background-color: var(--color-background);
  border: 1px solid var(--color-border-subtle);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  transition: all var(--duration-normal) var(--ease-out);
}

.card:hover {
  border-color: var(--color-border);
  box-shadow: var(--shadow-sm);
}

.card-header {
  margin-bottom: var(--spacing-4);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-foreground);
  margin-bottom: var(--spacing-1);
}

.card-description {
  font-size: var(--font-size-sm);
  color: var(--color-foreground-muted);
  line-height: var(--line-height-relaxed);
}

/* 输入框系统 */
.input {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  background-color: var(--color-background);
  color: var(--color-foreground);
  transition: all var(--duration-fast) var(--ease-out);
}

.input:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 3px rgba(10, 10, 10, 0.1);
}

.input::placeholder {
  color: var(--color-foreground-subtle);
}

/* ========================================
   布局系统
   ======================================== */

.container {
  max-width: 750rpx;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.section {
  padding: var(--spacing-16) 0;
}

.section-sm {
  padding: var(--spacing-12) 0;
}

.section-lg {
  padding: var(--spacing-24) 0;
}

/* Flexbox 工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 { gap: var(--spacing-1); }
.gap-2 { gap: var(--spacing-2); }
.gap-3 { gap: var(--spacing-3); }
.gap-4 { gap: var(--spacing-4); }
.gap-6 { gap: var(--spacing-6); }
.gap-8 { gap: var(--spacing-8); }

/* ========================================
   字体系统
   ======================================== */

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* 颜色工具类 */
.text-foreground { color: var(--color-foreground); }
.text-muted { color: var(--color-foreground-muted); }
.text-subtle { color: var(--color-foreground-subtle); }
.text-primary { color: var(--color-primary); }

/* ========================================
   间距系统
   ======================================== */

.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-6 { padding: var(--spacing-6); }
.p-8 { padding: var(--spacing-8); }

.px-1 { padding-left: var(--spacing-1); padding-right: var(--spacing-1); }
.px-2 { padding-left: var(--spacing-2); padding-right: var(--spacing-2); }
.px-4 { padding-left: var(--spacing-4); padding-right: var(--spacing-4); }
.px-6 { padding-left: var(--spacing-6); padding-right: var(--spacing-6); }

.py-1 { padding-top: var(--spacing-1); padding-bottom: var(--spacing-1); }
.py-2 { padding-top: var(--spacing-2); padding-bottom: var(--spacing-2); }
.py-4 { padding-top: var(--spacing-4); padding-bottom: var(--spacing-4); }
.py-6 { padding-top: var(--spacing-6); padding-bottom: var(--spacing-6); }

.m-1 { margin: var(--spacing-1); }
.m-2 { margin: var(--spacing-2); }
.m-4 { margin: var(--spacing-4); }
.m-6 { margin: var(--spacing-6); }

.mb-1 { margin-bottom: var(--spacing-1); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-6 { margin-bottom: var(--spacing-6); }
.mb-8 { margin-bottom: var(--spacing-8); }

.mt-1 { margin-top: var(--spacing-1); }
.mt-2 { margin-top: var(--spacing-2); }
.mt-4 { margin-top: var(--spacing-4); }
.mt-6 { margin-top: var(--spacing-6); }
.mt-8 { margin-top: var(--spacing-8); }

/* ========================================
   动画系统
   ======================================== */

.animate-fade-in {
  animation: fadeIn var(--duration-normal) var(--ease-out);
}

.animate-slide-up {
  animation: slideUp var(--duration-normal) var(--ease-out);
}

.animate-scale-in {
  animation: scaleIn var(--duration-fast) var(--ease-out);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ========================================
   状态系统
   ======================================== */

.loading {
  opacity: 0.6;
  pointer-events: none;
}

.disabled {
  opacity: 0.4;
  pointer-events: none;
}

.hidden {
  display: none;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}