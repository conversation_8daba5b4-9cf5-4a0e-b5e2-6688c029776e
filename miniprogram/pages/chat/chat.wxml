<!--pages/chat/chat.wxml-->
<view class="chat-page">

  <!-- 顶部状态栏 -->
  <view class="status-bar">
    <view class="conversation-stage">
      <text class="stage-title">{{conversationStages[currentStage].title}}</text>
      <text class="stage-subtitle">{{conversationStages[currentStage].description}}</text>
    </view>

    <view class="progress-indicator">
      <view class="progress-ring">
        <text class="progress-text">{{progress}}%</text>
      </view>
      <ui-button
        variant="ghost"
        size="sm"
        text="预览简历"
        className="preview-btn"
        bindtap="onPreviewResume"
      ></ui-button>
    </view>
  </view>

  <!-- 对话区域 -->
  <scroll-view
    class="conversation-area"
    scroll-y="{{true}}"
    scroll-top="{{scrollTop}}"
    scroll-with-animation="{{true}}"
    enhanced="{{true}}"
  >
    <view class="conversation-content">

      <!-- 欢迎消息 -->
      <view class="welcome-section">
        <view class="welcome-message">
          <text class="welcome-title">开始讲述你的故事</text>
          <text class="welcome-subtitle">我会认真倾听，帮你整理出一份精彩的简历</text>
        </view>
      </view>

      <!-- 消息列表 -->
      <view class="messages-list">
        <view
          class="message-group"
          wx:for="{{messages}}"
          wx:key="id"
        >
          <!-- AI 消息 -->
          <view wx:if="{{item.isAI}}" class="message ai-message">
            <view class="message-content">
              <text class="message-text">{{item.content}}</text>
              <text class="message-time">{{item.timestamp}}</text>
            </view>
          </view>

          <!-- 用户消息 -->
          <view wx:else class="message user-message">
            <view class="message-content">
              <text class="message-text">{{item.content}}</text>
              <text class="message-time">{{item.timestamp}}</text>
            </view>
          </view>
        </view>

        <!-- 输入状态指示器 -->
        <view wx:if="{{isTyping}}" class="message ai-message typing-message">
          <view class="message-content">
            <view class="typing-indicator">
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 快捷回复区域 -->
  <view wx:if="{{showQuickReplies && quickReplies.length > 0}}" class="quick-replies-area">
    <view class="quick-replies-header">
      <text class="quick-replies-title">建议回复</text>
    </view>
    <scroll-view class="quick-replies-scroll" scroll-x="{{true}}">
      <view class="quick-replies-list">
        <view
          class="quick-reply-chip"
          wx:for="{{quickReplies}}"
          wx:key="index"
          data-reply="{{item}}"
          bindtap="onQuickReply"
        >
          <text class="quick-reply-text">{{item}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 简历完整度指示器 -->
  <view wx:if="{{resumeData.completeness > 0}}" class="completeness-indicator">
    <view class="completeness-content">
      <view class="completeness-info">
        <text class="completeness-label">简历完整度</text>
        <text class="completeness-percentage">{{resumeData.completeness}}%</text>
      </view>
      <view class="completeness-bar">
        <view
          class="completeness-progress"
          style="width: {{resumeData.completeness}}%"
        ></view>
      </view>
    </view>
  </view>

  <!-- 输入区域 -->
  <view class="input-area">
    <view class="input-container">
      <input
        class="message-input input"
        type="text"
        placeholder="分享你的经历..."
        value="{{inputValue}}"
        bindinput="onInput"
        confirm-type="send"
        bindconfirm="onSend"
      />
      <ui-button
        variant="{{inputValue ? 'primary' : 'secondary'}}"
        size="default"
        text="发送"
        disabled="{{!inputValue}}"
        className="send-button"
        bindtap="onSend"
      ></ui-button>
    </view>
  </view>
</view>
