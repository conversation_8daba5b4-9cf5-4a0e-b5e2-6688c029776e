/* pages/chat/chat.wxss */

/**
 * 对话页面设计 - 专注于对话体验
 * 营造温暖、专业的对话氛围
 */

/* ========================================
   页面布局
   ======================================== */

.chat-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
}

/* ========================================
   顶部状态栏
   ======================================== */

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--color-border-subtle);
  background-color: var(--color-background);
  position: sticky;
  top: 0;
  z-index: 10;
}

.conversation-stage {
  flex: 1;
}

.stage-title {
  display: block;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-foreground);
  margin-bottom: var(--spacing-1);
}

.stage-subtitle {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--color-foreground-muted);
}

.progress-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.progress-ring {
  width: 60rpx;
  height: 60rpx;
  border: 2px solid var(--color-border);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.progress-text {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-foreground-muted);
}

.preview-btn {
  font-size: var(--font-size-sm);
  padding: var(--spacing-2) var(--spacing-3);
}

/* ========================================
   对话区域
   ======================================== */

.conversation-area {
  flex: 1;
  padding: 0 var(--spacing-4);
}

.conversation-content {
  max-width: 600rpx;
  margin: 0 auto;
  padding: var(--spacing-6) 0;
}

/* 欢迎区域 */
.welcome-section {
  text-align: center;
  margin-bottom: var(--spacing-12);
  padding: var(--spacing-8) 0;
}

.welcome-message {
  max-width: 400rpx;
  margin: 0 auto;
}

.welcome-title {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-foreground);
  margin-bottom: var(--spacing-2);
  line-height: var(--line-height-tight);
}

.welcome-subtitle {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--color-foreground-muted);
  line-height: var(--line-height-relaxed);
}

/* 消息列表 */
.messages-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.message-group {
  animation: slideUp var(--duration-normal) var(--ease-out);
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 80%;
}

.ai-message {
  align-self: flex-start;
}

.user-message {
  align-self: flex-end;
}

.message-content {
  background-color: var(--color-background-muted);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4) var(--spacing-5);
  position: relative;
}

.ai-message .message-content {
  background-color: var(--color-background-muted);
  border-bottom-left-radius: var(--radius-sm);
}

.user-message .message-content {
  background-color: var(--color-accent);
  color: var(--color-accent-foreground);
  border-bottom-right-radius: var(--radius-sm);
}

.message-text {
  display: block;
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-2);
}

.message-time {
  display: block;
  font-size: var(--font-size-xs);
  opacity: 0.6;
}

.user-message .message-time {
  color: var(--color-accent-foreground);
}

/* 输入状态指示器 */
.typing-message .message-content {
  padding: var(--spacing-4);
}

.typing-indicator {
  display: flex;
  gap: var(--spacing-1);
  align-items: center;
}

.typing-dot {
  width: 8rpx;
  height: 8rpx;
  background-color: var(--color-foreground-subtle);
  border-radius: 50%;
  animation: typingPulse 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingPulse {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.4;
  }
  30% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* ========================================
   快捷回复区域
   ======================================== */

.quick-replies-area {
  padding: var(--spacing-4);
  border-top: 1px solid var(--color-border-subtle);
  background-color: var(--color-background-subtle);
}

.quick-replies-header {
  margin-bottom: var(--spacing-3);
}

.quick-replies-title {
  font-size: var(--font-size-sm);
  color: var(--color-foreground-muted);
  font-weight: var(--font-weight-medium);
}

.quick-replies-scroll {
  white-space: nowrap;
}

.quick-replies-list {
  display: inline-flex;
  gap: var(--spacing-2);
  padding-bottom: var(--spacing-2);
}

.quick-reply-chip {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-2) var(--spacing-4);
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  white-space: nowrap;
}

.quick-reply-chip:hover {
  background-color: var(--color-background-muted);
  border-color: var(--color-foreground-subtle);
}

.quick-reply-chip:active {
  transform: scale(0.98);
}

.quick-reply-text {
  font-size: var(--font-size-sm);
  color: var(--color-foreground);
}

/* ========================================
   简历完整度指示器
   ======================================== */

.completeness-indicator {
  padding: var(--spacing-4);
  border-top: 1px solid var(--color-border-subtle);
  background-color: var(--color-background);
}

.completeness-content {
  max-width: 600rpx;
  margin: 0 auto;
}

.completeness-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.completeness-label {
  font-size: var(--font-size-sm);
  color: var(--color-foreground-muted);
  font-weight: var(--font-weight-medium);
}

.completeness-percentage {
  font-size: var(--font-size-sm);
  color: var(--color-foreground);
  font-weight: var(--font-weight-semibold);
}

.completeness-bar {
  height: 6rpx;
  background-color: var(--color-background-muted);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.completeness-progress {
  height: 100%;
  background-color: var(--color-accent);
  border-radius: var(--radius-sm);
  transition: width var(--duration-slow) var(--ease-out);
}

/* ========================================
   输入区域
   ======================================== */

.input-area {
  padding: var(--spacing-4);
  border-top: 1px solid var(--color-border-subtle);
  background-color: var(--color-background);
  position: sticky;
  bottom: 0;
}

.input-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  max-width: 600rpx;
  margin: 0 auto;
}

.message-input {
  flex: 1;
  min-height: 88rpx;
  border-radius: var(--radius-xl);
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
}

.send-button {
  min-width: 120rpx;
  height: 88rpx;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  font-weight: var(--font-weight-medium);
}

.send-button.active {
  background-color: var(--color-accent);
  color: var(--color-accent-foreground);
}

.send-button.inactive {
  background-color: var(--color-background-muted);
  color: var(--color-foreground-subtle);
}

.send-text {
  font-size: var(--font-size-sm);
}

/* ========================================
   响应式设计
   ======================================== */

@media (max-width: 600rpx) {
  .status-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .progress-indicator {
    align-self: flex-end;
  }

  .conversation-content {
    padding: var(--spacing-4) 0;
  }

  .message {
    max-width: 90%;
  }

  .welcome-title {
    font-size: var(--font-size-lg);
  }
}

/* 页面容器 */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* 顶部进度区域 */
.progress-header {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background: #ffffff;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.progress-info {
  flex: 1;
  margin-left: 24rpx;
}

.stage-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8rpx;
}

.stage-desc {
  display: block;
  font-size: 24rpx;
  color: #64748b;
}

.preview-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
  background: #f1f5f9;
  border-radius: 16rpx;
  min-width: 80rpx;
}

.preview-btn text {
  font-size: 20rpx;
  color: #475569;
  margin-top: 4rpx;
}

/* 消息容器 */
.messages-container {
  flex: 1;
  padding: 0 32rpx;
  overflow-y: auto;
  width: 100%;
  box-sizing: border-box;
}

.messages-list {
  padding: 32rpx 0;
  width: 100%;
  box-sizing: border-box;
}

/* 消息项 */
.message-item {
  margin-bottom: 32rpx;
  width: 100%;
  box-sizing: border-box;
}

/* 消息包装器 */
.message-wrapper {
  display: flex;
  align-items: flex-start;
  width: 100%;
  box-sizing: border-box;
  max-width: 100%;
}

/* AI消息样式 */
.ai-message {
  justify-content: flex-start;
}

.ai-message .avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #0ea5e9, #3b82f6);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.avatar-text {
  font-size: 32rpx;
  color: #ffffff;
}

/* 用户消息样式 */
.user-message {
  justify-content: flex-end;
  width: 100%;
  max-width: 100%;
}

.user-message .message-bubble {
  max-width: 80%;
  margin-left: auto;
  margin-right: 0;
}

/* 消息气泡 */
.message-bubble {
  max-width: 70%;
  padding: 24rpx 32rpx;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.ai-bubble {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 24rpx 24rpx 24rpx 8rpx;
  color: #1e293b;
}

.user-bubble {
  background: linear-gradient(135deg, #0ea5e9, #3b82f6);
  color: #ffffff;
  border-radius: 24rpx 24rpx 8rpx 24rpx;
}

.message-text {
  display: block;
  font-size: 28rpx;
  line-height: 1.6;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-all;
  color: inherit;
  white-space: pre-wrap;
  max-width: 100%;
}

.message-time {
  display: block;
  font-size: 20rpx;
  opacity: 0.6;
  margin-top: 8rpx;
  color: inherit;
}

/* 输入状态指示器 */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 0;
}

.typing-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #94a3b8;
  animation: typing-pulse 1.4s ease-in-out infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing-pulse {
  0%, 60%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 快捷回复 */
.quick-replies {
  padding: 24rpx 32rpx;
  background: #ffffff;
  border-top: 1px solid #e2e8f0;
}

.quick-replies-title {
  font-size: 24rpx;
  color: #64748b;
  margin-bottom: 16rpx;
}

.quick-replies-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.quick-reply-item {
  padding: 16rpx 24rpx;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 32rpx;
  font-size: 24rpx;
  color: #475569;
  transition: all 0.2s ease;
}

.quick-reply-item:active {
  background: #e2e8f0;
  transform: scale(0.98);
}

/* 完整度卡片 */
.completeness-card {
  margin: 24rpx 32rpx;
  padding: 32rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1e293b;
}

.completeness-score {
  font-size: 32rpx;
  font-weight: 700;
  color: #0ea5e9;
}

.completeness-bar {
  height: 8rpx;
  background: #e2e8f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.completeness-fill {
  height: 100%;
  background: linear-gradient(90deg, #22c55e, #16a34a);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.completeness-tips {
  text-align: center;
}

.tip-text {
  font-size: 24rpx;
  color: #64748b;
}

/* 底部输入区域 */
.input-area {
  padding: 24rpx 32rpx;
  background: #ffffff;
  border-top: 1px solid #e2e8f0;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.input-container {
  display: flex;
  align-items: center;
  background: #f8fafc;
  border-radius: 32rpx;
  padding: 8rpx;
}

.message-input {
  flex: 1;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  background: transparent;
  border: none;
  outline: none;
}

.send-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 24rpx;
  color: #64748b;
}

.send-btn.active {
  background: linear-gradient(135deg, #0ea5e9, #3b82f6);
  color: #ffffff;
}

.send-btn:active {
  transform: scale(0.95);
}

/* 图标字体 */
.iconfont {
  font-family: 'iconfont';
  font-size: 32rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .progress-header {
    padding: 24rpx;
  }

  .messages-container {
    padding: 0 24rpx;
  }

  .input-area {
    padding: 16rpx 24rpx;
  }

  .message-bubble {
    max-width: 85%;
  }

  .user-message .message-bubble {
    max-width: 85%;
  }
}
