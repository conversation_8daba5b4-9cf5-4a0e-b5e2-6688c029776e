/* pages/chat/chat.wxss */

/**
 * 对话页面设计 - 专注于对话体验
 * 营造温暖、专业的对话氛围
 */

/* ========================================
   页面布局
   ======================================== */

.chat-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

/* ========================================
   顶部状态栏
   ======================================== */

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1px solid #f0f0f0;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.conversation-stage {
  flex: 1;
}

.stage-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #0a0a0a;
  margin-bottom: 8rpx;
}

.stage-subtitle {
  display: block;
  font-size: 24rpx;
  color: #737373;
}

.progress-indicator {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.progress-ring {
  width: 60rpx;
  height: 60rpx;
  border: 2px solid #e5e5e5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.progress-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #737373;
}

.preview-btn {
  font-size: 28rpx;
  padding: 16rpx 24rpx;
}

/* ========================================
   对话区域
   ======================================== */

.conversation-area {
  flex: 1;
  padding: 0 32rpx;
}

.conversation-content {
  max-width: 600rpx;
  margin: 0 auto;
  padding: 48rpx 0;
}

/* 欢迎区域 */
.welcome-section {
  text-align: center;
  margin-bottom: 96rpx;
  padding: 64rpx 0;
}

.welcome-message {
  max-width: 400rpx;
  margin: 0 auto;
}

.welcome-title {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  color: #0a0a0a;
  margin-bottom: 16rpx;
  line-height: 1.25;
}

.welcome-subtitle {
  display: block;
  font-size: 28rpx;
  color: #737373;
  line-height: 1.75;
}

/* 消息列表 */
.messages-list {
  display: flex;
  flex-direction: column;
  gap: 48rpx;
}

.message-group {
  animation: slideUp 250ms cubic-bezier(0.16, 1, 0.3, 1);
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 80%;
}

.ai-message {
  align-self: flex-start;
}

.user-message {
  align-self: flex-end;
}

.message-content {
  background-color: #f5f5f5;
  border-radius: 16rpx;
  padding: 32rpx 40rpx;
  position: relative;
}

.ai-message .message-content {
  background-color: #f5f5f5;
  border-bottom-left-radius: 6rpx;
}

.user-message .message-content {
  background-color: #0a0a0a;
  color: #ffffff;
  border-bottom-right-radius: 6rpx;
}

.message-text {
  display: block;
  font-size: 32rpx;
  line-height: 1.75;
  margin-bottom: 16rpx;
}

.message-time {
  display: block;
  font-size: 24rpx;
  opacity: 0.6;
}

.user-message .message-time {
  color: #ffffff;
}

/* 输入状态指示器 */
.typing-message .message-content {
  padding: 32rpx;
}

.typing-indicator {
  display: flex;
  gap: 8rpx;
  align-items: center;
}

.typing-dot {
  width: 8rpx;
  height: 8rpx;
  background-color: #a3a3a3;
  border-radius: 50%;
  animation: typingPulse 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingPulse {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.4;
  }
  30% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* ========================================
   快捷回复区域
   ======================================== */

.quick-replies-area {
  padding: 32rpx;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.quick-replies-header {
  margin-bottom: 24rpx;
}

.quick-replies-title {
  font-size: 28rpx;
  color: #737373;
  font-weight: 500;
}

.quick-replies-scroll {
  white-space: nowrap;
}

.quick-replies-list {
  display: inline-flex;
  gap: 16rpx;
  padding-bottom: 16rpx;
}

.quick-reply-chip {
  display: inline-flex;
  align-items: center;
  padding: 16rpx 32rpx;
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
  border-radius: 24rpx;
  transition: all 150ms cubic-bezier(0.16, 1, 0.3, 1);
  white-space: nowrap;
}

.quick-reply-chip:active {
  background-color: #f5f5f5;
  border-color: #a3a3a3;
  transform: scale(0.98);
}

.quick-reply-text {
  font-size: 28rpx;
  color: #0a0a0a;
}

/* ========================================
   简历完整度指示器
   ======================================== */

.completeness-indicator {
  padding: 32rpx;
  border-top: 1px solid #f0f0f0;
  background-color: #ffffff;
}

.completeness-content {
  max-width: 600rpx;
  margin: 0 auto;
}

.completeness-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.completeness-label {
  font-size: 28rpx;
  color: #737373;
  font-weight: 500;
}

.completeness-percentage {
  font-size: 28rpx;
  color: #0a0a0a;
  font-weight: 600;
}

.completeness-bar {
  height: 6rpx;
  background-color: #f5f5f5;
  border-radius: 6rpx;
  overflow: hidden;
}

.completeness-progress {
  height: 100%;
  background-color: #0a0a0a;
  border-radius: 6rpx;
  transition: width 350ms cubic-bezier(0.16, 1, 0.3, 1);
}

/* ========================================
   输入区域
   ======================================== */

.input-area {
  padding: 32rpx;
  border-top: 1px solid #f0f0f0;
  background-color: #ffffff;
  position: sticky;
  bottom: 0;
}

.input-container {
  display: flex;
  align-items: center;
  gap: 24rpx;
  max-width: 600rpx;
  margin: 0 auto;
}

.message-input {
  flex: 1;
  min-height: 88rpx;
  border-radius: 24rpx;
  padding: 24rpx 32rpx;
  font-size: 32rpx;
}

.send-button {
  min-width: 120rpx;
  height: 88rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  transition: all 150ms cubic-bezier(0.16, 1, 0.3, 1);
  font-weight: 500;
}

.send-button.active {
  background-color: #0a0a0a;
  color: #ffffff;
}

.send-button.inactive {
  background-color: #f5f5f5;
  color: #a3a3a3;
}

.send-text {
  font-size: 28rpx;
}

/* ========================================
   响应式设计
   ======================================== */

/* ========================================
   响应式设计
   ======================================== */

@media (max-width: 600rpx) {
  .status-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }

  .progress-indicator {
    align-self: flex-end;
  }

  .conversation-content {
    padding: 32rpx 0;
  }

  .message {
    max-width: 90%;
  }

  .welcome-title {
    font-size: 36rpx;
  }
}
