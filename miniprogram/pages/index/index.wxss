/* pages/index/index.wxss */

/**
 * 首页设计 - 基于乔布斯和乔纳森的设计理念
 * 极简、专注、以内容为核心
 */

/* ========================================
   页面布局
   ======================================== */

.page {
  min-height: 100vh;
  background-color: var(--color-background);
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: var(--spacing-8) var(--spacing-4);
  max-width: 750rpx;
  margin: 0 auto;
  width: 100%;
}

/* ========================================
   Hero 区域 - 极简且有力
   ======================================== */

.hero {
  text-align: center;
  margin-bottom: var(--spacing-20);
}

.hero-content {
  max-width: 600rpx;
  margin: 0 auto;
}

.brand {
  margin-bottom: var(--spacing-16);
}

.brand-name {
  display: block;
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-foreground);
  margin-bottom: var(--spacing-2);
  letter-spacing: -0.02em;
}

.brand-tagline {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--color-foreground-subtle);
  font-weight: var(--font-weight-medium);
  letter-spacing: 0.1em;
  text-transform: uppercase;
}

.hero-message {
  margin-bottom: var(--spacing-12);
}

.hero-title {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-foreground);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-4);
}

.hero-subtitle {
  display: block;
  font-size: var(--font-size-base);
  color: var(--color-foreground-muted);
  line-height: var(--line-height-relaxed);
  max-width: 480rpx;
  margin: 0 auto;
}

/* ========================================
   核心价值主张 - 清晰的信息层级
   ======================================== */

.value-proposition {
  margin-bottom: var(--spacing-20);
}

.value-item {
  padding: var(--spacing-8) 0;
  border-bottom: 1px solid var(--color-border-subtle);
  transition: all var(--duration-normal) var(--ease-out);
}

.value-item:last-child {
  border-bottom: none;
}

.value-item:hover {
  background-color: var(--color-background-subtle);
  margin: 0 calc(-1 * var(--spacing-4));
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
  border-radius: var(--radius-base);
}

.value-content {
  max-width: 480rpx;
  margin: 0 auto;
  text-align: center;
}

.value-title {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-foreground);
  margin-bottom: var(--spacing-2);
}

.value-description {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--color-foreground-muted);
  line-height: var(--line-height-relaxed);
}

/* ========================================
   操作区域 - 专注且清晰
   ======================================== */

.action-area {
  padding: var(--spacing-8) var(--spacing-4);
  border-top: 1px solid var(--color-border-subtle);
  background-color: var(--color-background-subtle);
}

.auth-section,
.user-section {
  max-width: 480rpx;
  margin: 0 auto;
  text-align: center;
}

.auth-content,
.user-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-4);
}

.auth-message,
.welcome-message {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-foreground);
}

.auth-button {
  margin: var(--spacing-2) 0;
}

.auth-note {
  font-size: var(--font-size-xs);
  color: var(--color-foreground-subtle);
  line-height: var(--line-height-relaxed);
}

.action-buttons {
  display: flex;
  gap: var(--spacing-3);
  margin-top: var(--spacing-2);
}

/* ========================================
   辅助信息 - 低调且有用
   ======================================== */

.footer-info {
  padding: var(--spacing-6) var(--spacing-4);
  display: flex;
  justify-content: center;
  gap: var(--spacing-8);
  border-top: 1px solid var(--color-border-subtle);
}

.info-item {
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
}

.info-item:hover {
  transform: translateY(-2rpx);
}

.info-text {
  font-size: var(--font-size-sm);
  color: var(--color-foreground-subtle);
  font-weight: var(--font-weight-medium);
}

.info-item:hover .info-text {
  color: var(--color-foreground-muted);
}


/* ========================================
   响应式设计
   ======================================== */

@media (max-width: 600rpx) {
  .hero-title {
    font-size: var(--font-size-xl);
  }

  .brand-name {
    font-size: var(--font-size-3xl);
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .action-buttons .btn {
    width: 100%;
  }

  .footer-info {
    gap: var(--spacing-4);
  }
}